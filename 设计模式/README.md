# Android 面试设计模式详解

## 项目简介

本项目整理了Android面试中最常见的设计模式，包含：
- 每个模式的核心概念和实现
- Android Framework中的实际应用案例
- 简单易懂的Demo代码
- 面试常见问题和答案

## 设计模式分类

### 创建型模式 (Creational Patterns)
- **单例模式 (Singleton)** - 确保一个类只有一个实例
- **工厂模式 (Factory)** - 创建对象的接口，让子类决定实例化哪个类
- **建造者模式 (Builder)** - 分步骤构建复杂对象

### 结构型模式 (Structural Patterns)
- **适配器模式 (Adapter)** - 让不兼容的接口能够协同工作
- **装饰器模式 (Decorator)** - 动态地给对象添加新功能
- **外观模式 (Facade)** - 为复杂子系统提供简单接口
- **代理模式 (Proxy)** - 为其他对象提供代理以控制访问

### 行为型模式 (Behavioral Patterns)
- **观察者模式 (Observer)** - 定义对象间一对多的依赖关系
- **策略模式 (Strategy)** - 定义算法族，使它们可以互相替换
- **命令模式 (Command)** - 将请求封装成对象
- **状态模式 (State)** - 允许对象在内部状态改变时改变行为

## Android Framework 中的应用

### 系统级应用
- **单例模式**: ActivityManager, WindowManager
- **工厂模式**: LayoutInflater, BitmapFactory
- **建造者模式**: AlertDialog.Builder, Notification.Builder
- **适配器模式**: RecyclerView.Adapter, ListView.Adapter
- **观察者模式**: BroadcastReceiver, ContentObserver
- **策略模式**: Animation Interpolator
- **代理模式**: Binder机制, AIDL

## 项目结构

```
设计模式/
├── README.md                    # 项目说明
├── creational/                  # 创建型模式
│   ├── singleton/              # 单例模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── SingletonDemo.java # 实现示例
│   ├── factory/                # 工厂模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── FactoryDemo.java   # 实现示例
│   └── builder/                # 建造者模式
│       ├── README.md          # 模式说明和Android应用
│       └── BuilderDemo.java   # 实现示例
├── structural/                  # 结构型模式
│   ├── adapter/                # 适配器模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── AdapterDemo.java   # 实现示例
│   ├── decorator/              # 装饰器模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── DecoratorDemo.java # 实现示例
│   ├── facade/                 # 外观模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── FacadeDemo.java    # 实现示例
│   └── proxy/                  # 代理模式
│       ├── README.md          # 模式说明和Android应用
│       └── ProxyDemo.java     # 实现示例
├── behavioral/                  # 行为型模式
│   ├── observer/               # 观察者模式
│   │   ├── README.md          # 模式说明和Android应用
│   │   └── ObserverDemo.java  # 实现示例
│   └── strategy/               # 策略模式
│       ├── README.md          # 模式说明和Android应用
│       └── StrategyDemo.java  # 实现示例
└── examples/                    # 综合示例
    ├── android_framework_examples.md  # Android Framework中的模式应用
    └── interview_questions.md         # 面试题集和答案
```

## 学习建议

1. **理解核心思想**: 每个模式解决什么问题
2. **掌握实现方式**: 如何用代码实现
3. **了解应用场景**: 在Android中的具体应用
4. **练习编码**: 手写常见模式的实现
5. **总结对比**: 相似模式的区别和选择

## 面试重点

- 单例模式的线程安全实现
- 工厂模式 vs 建造者模式的使用场景
- 适配器模式在RecyclerView中的应用
- 观察者模式在Android事件系统中的体现
- MVP/MVVM架构中涉及的设计模式

---

**注意**: 本项目专注于Android面试中的实用性，每个模式都会结合Android开发的实际场景进行讲解。
