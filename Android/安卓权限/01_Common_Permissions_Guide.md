# 01 - Android 常用权限使用指南

## 概述
Android 的权限系统是其安全模型的核心，旨在保护用户的隐私和设备数据。正确地理解和使用权限，不仅是保证应用功能正常运行的前提，也是建立用户信任、符合应用商店政策的关键。本指南将详细介绍 Android 权限的分类、适配流程以及常用权限的使用方法。

---

## 1. 权限的分类

根据保护级别，Android 权限主要分为三类：

1.  **普通权限 (Normal Permissions)**
    -   **定义**: 这些权限涉及对应用沙箱外部数据的访问，但对用户隐私的风险很小。例如 `INTERNET`, `VIBRATE`, `BLUETOOTH`。
    -   **使用方式**: 只需在 `AndroidManifest.xml` 中声明即可。系统会在应用安装时**自动授予**，无需用户交互。

2.  **危险权限 (Dangerous Permissions)**
    -   **定义**: 这些权限涉及对用户敏感数据的访问，如联系人、存储、位置、摄像头等。
    -   **使用方式**: 从 Android 6.0 (API 23) 开始，必须在**运行时动态请求**，由用户明确授权后才能使用。这是权限适配的**核心和重点**。

3.  **特殊权限 (Special Permissions)**
    -   **定义**: 这类权限的授权操作比危险权限更敏感，需要用户跳转到系统特定的设置页面手动开启。例如“悬浮窗权限” (`SYSTEM_ALERT_WINDOW`) 和“修改系统设置” (`WRITE_SETTINGS`)。
    -   **使用方式**: 通常需要先通过 `Settings.canDrawOverlays()` 等方法检查权限，然后通过发送一个特定 `Action` 的 `Intent` 来引导用户到设置页面。

---

## 2. 运行时权限的适配流程 (核心)

适配危险权限是所有现代 Android 应用的必须步骤。

### a. 完整的三步流程

1.  **在 `AndroidManifest.xml` 中声明权限**
    ```xml
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    ```

2.  **检查并请求权限**
    在执行需要权限的操作之前，必须检查权限状态。这个过程最好封装成一个方法。

    ```java
    private static final int REQUEST_CAMERA_PERMISSION = 101;

    private void startCameraFeature() {
        // 检查权限
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            // 权限已被授予，直接执行功能
            openCamera();
        } else {
            // 权限未被授予，需要向用户解释并请求
            requestCameraPermission();
        }
    }
    ```

3.  **处理用户的响应**
    重写 `onRequestPermissionsResult` 方法来接收用户的选择结果。

    ```java
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_CAMERA_PERMISSION) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 用户授予了权限
                openCamera();
            } else {
                // 用户拒绝了权限
                // 最佳实践：向用户解释为什么需要这个权限，并提供一个再次请求或跳转到设置的选项
                Toast.makeText(this, "无法使用相机功能，因为权限被拒绝", Toast.LENGTH_LONG).show();
            }
        }
    }
    ```

### b. 解释权限请求的必要性 (ShouldShowRequestPermissionRationale)

-   **场景**: 如果用户**曾经拒绝过**一次权限请求，但没有勾选“不再询问”，那么 `shouldShowRequestPermissionRationale()` 方法会返回 `true`。
-   **作用**: 在这种情况下，你应该向用户展示一个对话框或 UI 元素，**详细解释**为什么你的应用需要这个权限，以提高用户授权的可能性。在用户理解后，再发起第二次请求。

---

## 3. 常用权限列表及使用场景

| 权限 (Manifest 常量) | 保护级别 | 使用场景与注意事项 |
| :--- | :--- | :--- |
| `INTERNET` | 普通 | 访问网络。所有需要联网的应用都必须声明。 |
| `ACCESS_NETWORK_STATE` | 普通 | 检查当前的网络连接状态（如 Wi-Fi, 移动数据）。 |
| `CAMERA` | **危险** | 访问摄像头进行拍照或录像。 |
| `READ_EXTERNAL_STORAGE` | **危险** | **(API 32 及以下)** 读取外部共享存储。从 API 33 开始被更细化的媒体权限取代。 |
| `WRITE_EXTERNAL_STORAGE` | **危险** | **(API 29 及以下)** 写入外部共享存储。从 API 30 (Android 11) 开始，由于分区存储的限制，此权限几乎不再有作用。 |
| `READ_MEDIA_IMAGES` | **危险** | **(API 33+)** 读取外部存储中的图片。 |
| `READ_MEDIA_VIDEO` | **危险** | **(API 33+)** 读取外部存储中的视频。 |
| `READ_MEDIA_AUDIO` | **危险** | **(API 33+)** 读取外部存储中的音频文件。 |
| `ACCESS_FINE_LOCATION` | **危险** | 获取精确的地理位置（GPS）。 |
| `ACCESS_COARSE_LOCATION` | **危险** | 获取大致的地理位置（网络基站、Wi-Fi）。 |
| `POST_NOTIFICATIONS` | **危险** | **(API 33+)** 发送通知。 |
| `FOREGROUND_SERVICE` | 普通 | **(API 28+)** 允许应用使用前台服务。 |
| `SYSTEM_ALERT_WINDOW` | **特殊** | 悬浮窗权限，允许应用在其他应用之上显示窗口。需要引导用户到系统设置页开启。 |

---

## 4. 最佳实践与现代方法

### 使用 Jetpack Activity Result API

手动处理 `requestPermissions` 和 `onRequestPermissionsResult` 比较繁琐且容易出错。Google 推荐使用 Jetpack 的 **Activity Result API** 来简化这个流程。

```java
public class MyActivity extends AppCompatActivity {
    // 1. 注册一个权限请求的回调契约
    private final ActivityResultLauncher<String> requestPermissionLauncher = 
        registerForActivityResult(new ActivityResultContracts.RequestPermission(), isGranted -> {
            if (isGranted) {
                // 权限被授予
                openCamera();
            } else {
                // 权限被拒绝
                Toast.makeText(this, "Camera permission denied", Toast.LENGTH_SHORT).show();
            }
        });

    private void onCameraButtonClick() {
        // 2. 发起权限请求
        requestPermissionLauncher.launch(Manifest.permission.CAMERA);
    }
}
```

这种方式将请求的发起和结果的处理解耦，代码更简洁、更易于维护，是目前处理权限请求的最佳实践。
